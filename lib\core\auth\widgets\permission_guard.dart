import 'package:flutter/material.dart';
import '../../../core/widgets/ak_widgets.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/theme/app_dimensions.dart';
import '../../../core/theme/dynamic_colors.dart';
import '../services/auth_service.dart';
import '../../../core/utils/app_logger.dart';

/// ودجت حماية الصلاحيات
/// يتحقق من صلاحية المستخدم قبل عرض المحتوى
class PermissionGuard extends StatefulWidget {
  /// الصلاحية المطلوبة
  final String permission;

  /// قائمة الصلاحيات المطلوبة (يجب توفر جميعها)
  final List<String>? requiredPermissions;

  /// قائمة الصلاحيات البديلة (يكفي توفر واحدة منها)
  final List<String>? anyPermissions;

  /// الودجت المراد عرضه إذا كانت الصلاحية متوفرة
  final Widget child;

  /// الودجت المراد عرضه إذا لم تكن الصلاحية متوفرة
  final Widget? fallback;

  /// هل يتم إخفاء الودجت بالكامل إذا لم تكن الصلاحية متوفرة
  final bool hideIfNoPermission;

  /// رسالة مخصصة لعدم وجود صلاحية
  final String? noPermissionMessage;

  /// هل يتم عرض رسالة خطأ مفصلة
  final bool showDetailedError;

  /// دالة تنفذ عند عدم وجود صلاحية
  final VoidCallback? onNoPermission;

  const PermissionGuard({
    Key? key,
    required this.permission,
    required this.child,
    this.requiredPermissions,
    this.anyPermissions,
    this.fallback,
    this.hideIfNoPermission = false,
    this.noPermissionMessage,
    this.showDetailedError = false,
    this.onNoPermission,
  }) : super(key: key);

  /// إنشاء حماية للوحدة
  factory PermissionGuard.module({
    Key? key,
    required String module,
    required Widget child,
    Widget? fallback,
    bool hideIfNoPermission = false,
    String? noPermissionMessage,
    bool showDetailedError = false,
    VoidCallback? onNoPermission,
  }) {
    return PermissionGuard(
      key: key,
      permission: 'view_$module',
      child: child,
      fallback: fallback,
      hideIfNoPermission: hideIfNoPermission,
      noPermissionMessage: noPermissionMessage,
      showDetailedError: showDetailedError,
      onNoPermission: onNoPermission,
    );
  }

  /// إنشاء حماية لعملية CRUD
  factory PermissionGuard.crud({
    Key? key,
    required String module,
    required String operation,
    required Widget child,
    Widget? fallback,
    bool hideIfNoPermission = false,
    String? noPermissionMessage,
    bool showDetailedError = false,
    VoidCallback? onNoPermission,
  }) {
    String permission;
    switch (operation.toLowerCase()) {
      case 'create':
      case 'add':
        permission = 'add_${module.replaceAll('s', '')}';
        break;
      case 'read':
      case 'view':
        permission = 'view_$module';
        break;
      case 'update':
      case 'edit':
        permission = 'edit_${module.replaceAll('s', '')}';
        break;
      case 'delete':
        permission = 'delete_${module.replaceAll('s', '')}';
        break;
      default:
        permission = 'view_$module';
    }

    return PermissionGuard(
      key: key,
      permission: permission,
      child: child,
      fallback: fallback,
      hideIfNoPermission: hideIfNoPermission,
      noPermissionMessage: noPermissionMessage,
      showDetailedError: showDetailedError,
      onNoPermission: onNoPermission,
    );
  }

  @override
  State<PermissionGuard> createState() => _PermissionGuardState();
}

class _PermissionGuardState extends State<PermissionGuard> {
  final AuthService _authService = AuthService();
  bool _hasPermission = false;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// التحقق من الصلاحيات
  Future<void> _checkPermissions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      bool hasPermission = false;

      // التحقق من الصلاحية الأساسية
      if (widget.permission.isNotEmpty) {
        hasPermission = await _authService.hasPermission(widget.permission);
      }

      // التحقق من الصلاحيات المطلوبة (جميعها)
      if (!hasPermission && widget.requiredPermissions != null) {
        hasPermission = await _authService.hasAllPermissions(widget.requiredPermissions!);
      }

      // التحقق من الصلاحيات البديلة (واحدة منها)
      if (!hasPermission && widget.anyPermissions != null) {
        hasPermission = await _authService.hasAnyPermission(widget.anyPermissions!);
      }

      setState(() {
        _hasPermission = hasPermission;
        _isLoading = false;
      });

      // تنفيذ دالة عدم وجود صلاحية
      if (!hasPermission && widget.onNoPermission != null) {
        widget.onNoPermission!();
      }

      AppLogger.info('🔐 نتيجة فحص الصلاحية ${widget.permission}: ${hasPermission ? 'مسموح' : 'مرفوض'}');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasPermission = false;
        _errorMessage = 'خطأ في التحقق من الصلاحية: $e';
      });
      AppLogger.error('❌ خطأ في فحص الصلاحية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض مؤشر التحميل أثناء فحص الصلاحيات
    if (_isLoading) {
      return const Center(
        child: AkLoadingIndicator(
          size: 24,
          message: 'جاري التحقق من الصلاحيات...',
        ),
      );
    }

    // إذا كان هناك خطأ وطُلب عرض تفاصيل الخطأ
    if (_errorMessage != null && widget.showDetailedError) {
      return _buildErrorWidget();
    }

    // إذا كانت الصلاحية متوفرة، عرض المحتوى
    if (_hasPermission) {
      return widget.child;
    }

    // إذا لم تكن الصلاحية متوفرة
    if (widget.hideIfNoPermission) {
      return const SizedBox.shrink();
    }

    // عرض الودجت البديل أو رسالة عدم وجود صلاحية
    return widget.fallback ?? _buildNoPermissionWidget();
  }

  /// بناء ودجت عدم وجود صلاحية
  Widget _buildNoPermissionWidget() {
    return AkCard(
      padding: AppDimensions.paddingMedium,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock_outline,
            size: 48,
            color: DynamicColors.textSecondary(context),
          ),
          const SizedBox(height: AppDimensions.spacingMedium),
          Text(
            widget.noPermissionMessage ?? 'ليس لديك صلاحية للوصول إلى هذا المحتوى',
            style: AppTypography.bodyMedium.copyWith(
              color: DynamicColors.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.showDetailedError && widget.permission.isNotEmpty) ...[
            const SizedBox(height: AppDimensions.spacingSmall),
            Text(
              'الصلاحية المطلوبة: ${widget.permission}',
              style: AppTypography.bodySmall.copyWith(
                color: DynamicColors.textSecondary(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// بناء ودجت الخطأ
  Widget _buildErrorWidget() {
    return AkCard(
      padding: AppDimensions.paddingMedium,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppColors.error,
          ),
          const SizedBox(height: AppDimensions.spacingMedium),
          Text(
            'خطأ في التحقق من الصلاحيات',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingSmall),
          Text(
            _errorMessage ?? 'خطأ غير معروف',
            style: AppTypography.bodySmall.copyWith(
              color: DynamicColors.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingMedium),
          AkButton(
            text: 'إعادة المحاولة',
            onPressed: _checkPermissions,
            size: AkButtonSize.small,
          ),
        ],
      ),
    );
  }
}
