import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
//import 'package:tajer_plus/core/theme/index.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
import '../services/session_manager.dart';
import '../services/backup_service.dart';
import 'app_logger.dart';

import 'package:connectivity_plus/connectivity_plus.dart';

/// مدير دورة حياة التطبيق المحسن
///
/// يقوم بمراقبة حالة التطبيق وإدارة فترات الخمول وتسجيل الخروج التلقائي
/// كما يقوم بإنشاء نسخة احتياطية عند وضع التطبيق في الخلفية
/// ويتحقق من حالة الاتصال بالإنترنت ويدير عمليات المزامنة
/// ويراقب استخدام موارد النظام ويحسن أداء التطبيق
class AppLifecycleManager extends StatefulWidget {
  /// الويدجت الابن
  final Widget child;

  /// مدة الخمول بالدقائق قبل تسجيل الخروج التلقائي
  final int inactivityTimeoutMinutes;

  /// هل يتم إنشاء نسخة احتياطية عند وضع التطبيق في الخلفية
  final bool createBackupOnBackground;

  /// الفترة الزمنية بين النسخ الاحتياطية التلقائية (بالساعات)
  final int autoBackupIntervalHours;

  /// هل يتم مراقبة حالة الاتصال بالإنترنت
  final bool monitorConnectivity;

  /// هل يتم مراقبة استخدام موارد النظام
  final bool monitorSystemResources;

  /// الفترة الزمنية بين عمليات تنظيف الذاكرة (بالدقائق)
  final int memoryCleanupIntervalMinutes;

  /// دالة تنفذ عند تسجيل الخروج التلقائي
  final VoidCallback? onAutoLogout;

  /// دالة تنفذ عند تغير حالة الاتصال بالإنترنت
  final Function(bool isConnected)? onConnectivityChanged;

  /// دالة تنفذ عند تجاوز استخدام الذاكرة للحد المسموح
  final VoidCallback? onMemoryWarning;

  const AppLifecycleManager({
    Key? key,
    required this.child,
    this.inactivityTimeoutMinutes = 30,
    this.createBackupOnBackground = true,
    this.autoBackupIntervalHours = 24,
    this.monitorConnectivity = true,
    this.monitorSystemResources = true,
    this.memoryCleanupIntervalMinutes = 60,
    this.onAutoLogout,
    this.onConnectivityChanged,
    this.onMemoryWarning,
  }) : super(key: key);

  @override
  State<AppLifecycleManager> createState() => _AppLifecycleManagerState();
}

class _AppLifecycleManagerState extends State<AppLifecycleManager>
    with WidgetsBindingObserver {
  Timer? _inactivityTimer;
  Timer? _autoBackupTimer;
  Timer? _resourceMonitorTimer;
  DateTime? _lastUserActivity;
  DateTime? _lastBackupTime;
  DateTime? _lastMemoryCleanup;
  bool _isLoggedOut = false;
  bool _isConnected = true;
  bool _isMemoryWarningIssued = false;
  final Connectivity _connectivity = Connectivity();

  // حدود استخدام الموارد
  static const int _memoryWarningThresholdMB =
      200; // الحد الأقصى للذاكرة بالميجابايت قبل إصدار تحذير

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _resetInactivityTimer();
    _setupAutoBackupTimer();

    // إضافة مستمع للمدخلات لتحديث وقت آخر نشاط للمستخدم
    ServicesBinding.instance.keyboard.addHandler(_handleKeyboardActivity);

    // إعداد مراقبة الاتصال بالإنترنت
    if (widget.monitorConnectivity) {
      _setupConnectivityMonitoring();
    }

    // إعداد مراقبة موارد النظام
    if (widget.monitorSystemResources) {
      _setupResourceMonitoring();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _inactivityTimer?.cancel();
    _autoBackupTimer?.cancel();
    _resourceMonitorTimer?.cancel();
    ServicesBinding.instance.keyboard.removeHandler(_handleKeyboardActivity);
    super.dispose();
  }

  /// إعداد مؤقت النسخ الاحتياطي التلقائي
  void _setupAutoBackupTimer() {
    // تحقق من آخر نسخة احتياطية
    _checkLastBackupTime();

    // إعداد مؤقت للنسخ الاحتياطي التلقائي
    _autoBackupTimer = Timer.periodic(
      Duration(hours: widget.autoBackupIntervalHours),
      (_) => _performScheduledBackup(),
    );
  }

  /// التحقق من وقت آخر نسخة احتياطية
  Future<void> _checkLastBackupTime() async {
    try {
      // استرجاع وقت آخر نسخة احتياطية من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final lastBackupTimeStr = prefs.getString('last_backup_time');

      if (lastBackupTimeStr != null) {
        _lastBackupTime = DateTime.parse(lastBackupTimeStr);
        AppLogger.info(
            'تم استرجاع وقت آخر نسخة احتياطية: ${_lastBackupTime.toString()}');
      } else {
        AppLogger.info('لم يتم العثور على سجل لآخر نسخة احتياطية');
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء التحقق من وقت آخر نسخة احتياطية: $e');
    }
  }

  /// إجراء النسخ الاحتياطي المجدول
  Future<void> _performScheduledBackup() async {
    try {
      final isLoggedIn = await SessionManager.isLoggedIn();
      if (!isLoggedIn) return;

      // التحقق من الوقت المنقضي منذ آخر نسخة احتياطية
      if (_lastBackupTime != null) {
        final hoursSinceLastBackup =
            DateTime.now().difference(_lastBackupTime!).inHours;
        if (hoursSinceLastBackup < widget.autoBackupIntervalHours) {
          return; // لم يحن وقت النسخ الاحتياطي بعد
        }
      }

      // إنشاء نسخة احتياطية
      AppLogger.info('بدء إنشاء نسخة احتياطية مجدولة');
      final backupFile = await BackupService.createBackup();

      if (backupFile != null) {
        // تحديث وقت آخر نسخة احتياطية
        _lastBackupTime = DateTime.now();

        // حفظ وقت آخر نسخة احتياطية في التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'last_backup_time', _lastBackupTime!.toIso8601String());

        AppLogger.info(
            'تم إنشاء النسخة الاحتياطية المجدولة بنجاح: ${backupFile.path}');
      } else {
        AppLogger.warning('فشل في إنشاء النسخة الاحتياطية المجدولة');
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء إجراء النسخ الاحتياطي المجدول: $e');
    }
  }

  /// إعداد مراقبة الاتصال بالإنترنت
  void _setupConnectivityMonitoring() {
    // الاستماع لتغييرات الاتصال
    _connectivity.onConnectivityChanged.listen((result) {
      final hasConnection = result != ConnectivityResult.none;
      if (_isConnected != hasConnection) {
        _isConnected = hasConnection;
        AppLogger.info(
            'تغيير حالة الاتصال: ${hasConnection ? 'متصل' : 'غير متصل'}');

        if (widget.onConnectivityChanged != null) {
          widget.onConnectivityChanged!(_isConnected);
        }

        // إذا عاد الاتصال، يمكن تنفيذ عمليات المزامنة
        if (_isConnected) {
          _performSyncOperations();
        }
      }
    });

    // التحقق من حالة الاتصال الحالية
    _checkConnectivity();
  }

  /// إعداد مراقبة موارد النظام
  void _setupResourceMonitoring() {
    // إعداد مؤقت لمراقبة موارد النظام
    _resourceMonitorTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _monitorSystemResources(),
    );

    // تنفيذ تنظيف الذاكرة بشكل دوري
    Timer.periodic(
      Duration(minutes: widget.memoryCleanupIntervalMinutes),
      (_) {
        // التحقق من الوقت المنقضي منذ آخر تنظيف للذاكرة
        final now = DateTime.now();
        if (_lastMemoryCleanup == null ||
            now.difference(_lastMemoryCleanup!).inMinutes >=
                widget.memoryCleanupIntervalMinutes) {
          _performMemoryCleanup();
        } else {
          AppLogger.info(
              'تم تخطي تنظيف الذاكرة لأن آخر تنظيف كان منذ ${now.difference(_lastMemoryCleanup!).inMinutes} دقيقة');
        }
      },
    );
  }

  /// مراقبة موارد النظام
  Future<void> _monitorSystemResources() async {
    try {
      // التحقق من استخدام الذاكرة
      final memoryInfo = await _getMemoryInfo();
      final usedMemoryMB = memoryInfo['used'] ?? 0;

      AppLogger.info('استخدام الذاكرة الحالي: $usedMemoryMB ميجابايت');

      // إصدار تحذير إذا تجاوز الحد المسموح
      if (usedMemoryMB > _memoryWarningThresholdMB && !_isMemoryWarningIssued) {
        _isMemoryWarningIssued = true;
        AppLogger.warning(
            'تحذير: استخدام الذاكرة مرتفع ($usedMemoryMB ميجابايت)');

        // استدعاء دالة التحذير إذا كانت موجودة
        if (widget.onMemoryWarning != null) {
          widget.onMemoryWarning!();
        }

        // تنفيذ تنظيف الذاكرة تلقائيًا
        await _performMemoryCleanup();
      } else if (usedMemoryMB <= _memoryWarningThresholdMB &&
          _isMemoryWarningIssued) {
        _isMemoryWarningIssued = false;
        AppLogger.info('تم تخفيض استخدام الذاكرة إلى المستوى الطبيعي');
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء مراقبة موارد النظام: $e');
    }
  }

  /// الحصول على معلومات استخدام الذاكرة
  Future<Map<String, int>> _getMemoryInfo() async {
    try {
      // هذه طريقة تقريبية لتقدير استخدام الذاكرة
      // في التطبيقات الحقيقية، يمكن استخدام مكتبات متخصصة
      final tempDir = await getTemporaryDirectory();
      final cacheDir = await getApplicationCacheDirectory();

      int totalSize = 0;

      // حساب حجم الملفات المؤقتة
      if (await tempDir.exists()) {
        totalSize += await _calculateDirSize(tempDir);
      }

      // حساب حجم ملفات التخزين المؤقت
      if (await cacheDir.exists()) {
        totalSize += await _calculateDirSize(cacheDir);
      }

      // تحويل الحجم من بايت إلى ميجابايت
      final usedMemoryMB = (totalSize / (1024 * 1024)).round();

      return {
        'used': usedMemoryMB,
        'total': 0, // غير متاح بشكل مباشر في Flutter
      };
    } catch (e) {
      AppLogger.error('خطأ أثناء الحصول على معلومات الذاكرة: $e');
      return {'used': 0, 'total': 0};
    }
  }

  /// حساب حجم المجلد
  Future<int> _calculateDirSize(Directory dir) async {
    try {
      int totalSize = 0;
      final lister = dir.list(recursive: true, followLinks: false);
      await for (final file in lister) {
        if (file is File) {
          totalSize += await file.length();
        }
      }
      return totalSize;
    } catch (e) {
      AppLogger.error('خطأ أثناء حساب حجم المجلد: $e');
      return 0;
    }
  }

  /// تنظيف الذاكرة
  Future<void> _performMemoryCleanup() async {
    try {
      _lastMemoryCleanup = DateTime.now();
      AppLogger.info('بدء تنظيف الذاكرة والملفات المؤقتة');

      // تنظيف الملفات المؤقتة
      final tempDir = await getTemporaryDirectory();
      final cacheDir = await getApplicationCacheDirectory();

      // حذف الملفات المؤقتة القديمة (أقدم من 7 أيام)
      int deletedTempFiles = 0;
      int deletedCacheFiles = 0;
      final cutoffDate = DateTime.now().subtract(const Duration(days: 7));

      // تنظيف مجلد الملفات المؤقتة
      if (await tempDir.exists()) {
        await for (final entity
            in tempDir.list(recursive: true, followLinks: false)) {
          if (entity is File) {
            try {
              final stat = await entity.stat();
              final fileModTime = DateTime.fromMillisecondsSinceEpoch(
                  stat.modified.millisecondsSinceEpoch);

              if (fileModTime.isBefore(cutoffDate)) {
                await entity.delete();
                deletedTempFiles++;
              }
            } catch (e) {
              // تجاهل الأخطاء للملفات الفردية
              AppLogger.warning('تعذر حذف الملف المؤقت: ${entity.path}');
            }
          }
        }
      }

      // تنظيف مجلد التخزين المؤقت
      if (await cacheDir.exists()) {
        await for (final entity
            in cacheDir.list(recursive: true, followLinks: false)) {
          if (entity is File) {
            try {
              final stat = await entity.stat();
              final fileModTime = DateTime.fromMillisecondsSinceEpoch(
                  stat.modified.millisecondsSinceEpoch);

              if (fileModTime.isBefore(cutoffDate)) {
                await entity.delete();
                deletedCacheFiles++;
              }
            } catch (e) {
              // تجاهل الأخطاء للملفات الفردية
              AppLogger.warning('تعذر حذف ملف التخزين المؤقت: ${entity.path}');
            }
          }
        }
      }

      // تنظيف ذاكرة التخزين المؤقت للصور (إذا كانت مستخدمة)
      try {
        // يمكن إضافة تنظيف لمكتبات الصور المستخدمة مثل cached_network_image
        // PaintingBinding.instance.imageCache?.clear();
        // PaintingBinding.instance.imageCache?.clearLiveImages();
      } catch (e) {
        AppLogger.warning('تعذر تنظيف ذاكرة التخزين المؤقت للصور: $e');
      }

      AppLogger.info(
          'تم تنظيف الذاكرة والملفات المؤقتة بنجاح (تم حذف $deletedTempFiles ملف مؤقت و $deletedCacheFiles ملف تخزين مؤقت)');
    } catch (e) {
      AppLogger.error('خطأ أثناء تنظيف الذاكرة والملفات المؤقتة: $e');
    }
  }

  /// معالجة نشاط لوحة المفاتيح لتحديث وقت آخر نشاط للمستخدم
  bool _handleKeyboardActivity(KeyEvent event) {
    _resetInactivityTimer();
    return false; // السماح للحدث بالاستمرار في المعالجة
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    AppLogger.info('تغيير حالة دورة حياة التطبيق: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // التطبيق في المقدمة ونشط
        _resetInactivityTimer();
        _isLoggedOut = false;
        _checkConnectivity();
        break;
      case AppLifecycleState.inactive:
        // التطبيق غير نشط (مثلاً عند استقبال مكالمة)
        _lastUserActivity = DateTime.now();
        break;
      case AppLifecycleState.paused:
        // التطبيق في الخلفية
        if (widget.createBackupOnBackground) {
          _createBackup(isAutomatic: true);
        }
        break;
      case AppLifecycleState.detached:
        // التطبيق منفصل (قد يكون مغلقاً)
        if (widget.createBackupOnBackground) {
          _createBackup(isAutomatic: true);
        }
        break;
      default:
        break;
    }
  }

  /// إعادة ضبط مؤقت الخمول
  void _resetInactivityTimer() {
    _lastUserActivity = DateTime.now();
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _checkInactivity();
    });
  }

  /// التحقق من فترة الخمول
  void _checkInactivity() {
    if (_lastUserActivity == null || _isLoggedOut) return;

    final now = DateTime.now();
    final difference = now.difference(_lastUserActivity!);

    if (difference.inMinutes >= widget.inactivityTimeoutMinutes) {
      // تسجيل الخروج التلقائي بعد فترة الخمول
      _performAutoLogout();
    }
  }

  /// تنفيذ تسجيل الخروج التلقائي
  void _performAutoLogout() {
    if (_isLoggedOut) return;

    _isLoggedOut = true;
    _inactivityTimer?.cancel();

    AppLogger.info('تم تسجيل الخروج تلقائياً بسبب الخمول');

    // إنشاء نسخة احتياطية قبل تسجيل الخروج
    if (widget.createBackupOnBackground) {
      _createBackup();
    }

    // تنفيذ دالة تسجيل الخروج إذا كانت موجودة
    if (widget.onAutoLogout != null) {
      widget.onAutoLogout!();
    }
  }

  /// التحقق من حالة الاتصال بالإنترنت
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      final hasConnection = result != ConnectivityResult.none;

      if (_isConnected != hasConnection) {
        _isConnected = hasConnection;
        AppLogger.info(
            'تغيير حالة الاتصال: ${hasConnection ? 'متصل' : 'غير متصل'}');

        if (widget.onConnectivityChanged != null) {
          widget.onConnectivityChanged!(_isConnected);
        }

        // إذا عاد الاتصال، يمكن تنفيذ عمليات المزامنة
        if (_isConnected) {
          _performSyncOperations();
        }
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء التحقق من حالة الاتصال: $e');
    }
  }

  /// تنفيذ عمليات المزامنة عند عودة الاتصال
  Future<void> _performSyncOperations() async {
    try {
      final isLoggedIn = await SessionManager.isLoggedIn();
      if (!isLoggedIn) return;

      AppLogger.info('بدء عمليات المزامنة بعد استعادة الاتصال');

      // مزامنة البيانات مع الخادم
      await _syncDataWithServer();

      // تحميل التحديثات المعلقة
      await _downloadPendingUpdates();

      // إرسال البيانات المخزنة محلياً
      await _uploadLocalData();

      AppLogger.info('تم الانتهاء من عمليات المزامنة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ أثناء تنفيذ عمليات المزامنة: $e');
    }
  }

  /// مزامنة البيانات مع الخادم
  Future<void> _syncDataWithServer() async {
    try {
      AppLogger.info('جاري مزامنة البيانات مع الخادم...');

      // التحقق من وجود تحديثات جديدة على الخادم
      final prefs = await SharedPreferences.getInstance();
      final lastSyncTime = prefs.getString('last_sync_time');
      final deviceId = await _getDeviceId();

      // هنا يمكن استدعاء خدمة المزامنة الفعلية
      // في تطبيق حقيقي، هذا سيكون اتصالاً بالخادم مع إرسال المعلمات التالية:
      // - lastSyncTime: وقت آخر مزامنة
      // - deviceId: معرف الجهاز
      // - appVersion: إصدار التطبيق
      AppLogger.info(
          'مزامنة البيانات للجهاز: $deviceId، آخر مزامنة: ${lastSyncTime ?? "لا توجد"}');

      // تحديث وقت آخر مزامنة
      await prefs.setString('last_sync_time', DateTime.now().toIso8601String());

      AppLogger.info('تمت مزامنة البيانات مع الخادم بنجاح');
    } catch (e) {
      AppLogger.error('خطأ أثناء مزامنة البيانات مع الخادم: $e');
      rethrow;
    }
  }

  /// تحميل التحديثات المعلقة
  Future<void> _downloadPendingUpdates() async {
    try {
      AppLogger.info('جاري تحميل التحديثات المعلقة...');

      // التحقق من وجود تحديثات جديدة
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateCheck = prefs.getString('last_update_check');
      final now = DateTime.now();
      final deviceId = await _getDeviceId();

      // في تطبيق حقيقي، هذا سيكون اتصالاً بالخادم لتحميل التحديثات
      // مثل تحديثات المنتجات، الأسعار، العروض، إلخ
      AppLogger.info(
          'فحص التحديثات للجهاز: $deviceId، آخر فحص: ${lastUpdateCheck ?? "لا يوجد"}، '
          'النظام: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

      // تحديث وقت آخر فحص للتحديثات
      await prefs.setString('last_update_check', now.toIso8601String());

      // تطبيق التحديثات المحملة على قاعدة البيانات المحلية
      // في تطبيق حقيقي، هذا سيكون تحديثاً لقاعدة البيانات المحلية

      AppLogger.info('تم تحميل التحديثات المعلقة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ أثناء تحميل التحديثات المعلقة: $e');
      rethrow;
    }
  }

  /// إرسال البيانات المخزنة محلياً
  Future<void> _uploadLocalData() async {
    try {
      AppLogger.info('جاري إرسال البيانات المخزنة محلياً...');

      // البحث عن البيانات غير المتزامنة
      // في تطبيق حقيقي، هذا سيكون استعلاماً لقاعدة البيانات المحلية
      // للبحث عن السجلات التي تحتاج إلى مزامنة
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      // تحديد أنواع البيانات التي تحتاج إلى مزامنة
      final dataTypesToSync = [
        'transactions',
        'inventory_changes',
        'customer_updates',
        'orders',
        'payments'
      ];

      int totalSyncedItems = 0;

      // لكل نوع من البيانات، قم بالبحث عن السجلات غير المتزامنة وإرسالها
      for (final dataType in dataTypesToSync) {
        // في تطبيق حقيقي، هذا سيكون استعلاماً لقاعدة البيانات المحلية
        // للبحث عن السجلات التي تحتاج إلى مزامنة لهذا النوع من البيانات

        // إرسال البيانات إلى الخادم على دفعات لتحسين الأداء
        // في تطبيق حقيقي، هذا سيكون اتصالاً بالخادم لإرسال البيانات

        // تحديث حالة المزامنة للسجلات المرسلة
        // في تطبيق حقيقي، هذا سيكون تحديثاً لقاعدة البيانات المحلية

        AppLogger.info('تمت مزامنة بيانات $dataType بنجاح');
        totalSyncedItems +=
            1; // في التطبيق الحقيقي، هذا سيكون عدد السجلات المرسلة فعلياً
      }

      // تحديث وقت آخر مزامنة للبيانات المرسلة
      await prefs.setString('last_sync_upload', now.toIso8601String());

      // التعامل مع حالات الفشل وإعادة المحاولة
      // في تطبيق حقيقي، يمكن تخزين السجلات التي فشل إرسالها لإعادة المحاولة لاحقاً

      AppLogger.info(
          'تم إرسال البيانات المخزنة محلياً بنجاح (تمت مزامنة $totalSyncedItems عنصر)');
    } catch (e) {
      AppLogger.error('خطأ أثناء إرسال البيانات المخزنة محلياً: $e');
      rethrow;
    }
  }

  /// الحصول على معرف الجهاز
  ///
  /// يقوم باسترجاع معرف الجهاز من التخزين المحلي
  /// إذا لم يكن موجوداً، يقوم بإنشاء معرف جديد وتخزينه
  /// يعيد دائماً قيمة غير قابلة لأن تكون null
  Future<String> _getDeviceId() async {
    try {
      // الحصول على مثيل من SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // محاولة استرجاع معرف الجهاز من التخزين المحلي
      String? deviceId = prefs.getString('device_id');

      // إذا لم يكن هناك معرف مخزن، قم بإنشاء معرف جديد
      if (deviceId.isEmpty) {
        // إنشاء معرف فريد باستخدام مكتبة UUID
        deviceId = const Uuid().v4();

        // تخزين المعرف الجديد في التخزين المحلي للاستخدام لاحقاً
        await prefs.setString('device_id', deviceId);

        AppLogger.info('تم إنشاء معرف جهاز جديد: $deviceId');
      } else {
        AppLogger.info('تم استرجاع معرف الجهاز: $deviceId');
      }

      // إعادة المعرف (مضمون أنه ليس null)
      return deviceId;
    } catch (e) {
      // في حالة حدوث خطأ، سجل الخطأ وأنشئ معرفًا احتياطيًا
      AppLogger.error('خطأ أثناء الحصول على معرف الجهاز: $e');

      // إنشاء معرف احتياطي باستخدام الطابع الزمني
      final fallbackId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';

      // محاولة تخزين المعرف الاحتياطي
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('device_id', fallbackId);
      } catch (storageError) {
        // تجاهل أي خطأ في التخزين
        AppLogger.error('فشل تخزين معرف الجهاز الاحتياطي: $storageError');
      }

      return fallbackId;
    }
  }

  // تم استخدام هذه الدالة في _monitorSystemResources عند الحاجة لتحسين أداء التطبيق
  // عند ارتفاع استخدام الذاكرة

  /// إنشاء نسخة احتياطية
  Future<void> _createBackup({bool isAutomatic = false}) async {
    try {
      final isLoggedIn = await SessionManager.isLoggedIn();
      if (isLoggedIn) {
        AppLogger.info(
            'إنشاء نسخة احتياطية ${isAutomatic ? 'تلقائية' : 'مجدولة'}');

        final backupFile = await BackupService.createBackup();
        if (backupFile != null) {
          _lastBackupTime = DateTime.now();
          AppLogger.info(
              'تم إنشاء النسخة الاحتياطية بنجاح: ${backupFile.path}');

          // حفظ وقت آخر نسخة احتياطية
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(
              'last_backup_time', _lastBackupTime!.toIso8601String());

          // حذف النسخ الاحتياطية القديمة للحفاظ على مساحة التخزين
          await _cleanupOldBackups();
        }
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء إنشاء النسخة الاحتياطية: $e');
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  Future<void> _cleanupOldBackups() async {
    try {
      // الحصول على مجلد النسخ الاحتياطية
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir =
          Directory('${appDir.path}/${AppConstants.backupDirectory}');

      if (!await backupDir.exists()) {
        return;
      }

      // الحصول على قائمة ملفات النسخ الاحتياطية
      final backupFiles = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.zip'))
          .cast<File>()
          .toList();

      // الاحتفاظ بآخر 5 نسخ احتياطية فقط
      const int maxBackupsToKeep = 5;

      if (backupFiles.length > maxBackupsToKeep) {
        // ترتيب الملفات حسب تاريخ التعديل (الأقدم أولاً)
        backupFiles.sort(
            (a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()));

        // حذف النسخ الاحتياطية الأقدم
        final filesToDelete =
            backupFiles.sublist(0, backupFiles.length - maxBackupsToKeep);

        for (final file in filesToDelete) {
          await file.delete();
          AppLogger.info('تم حذف النسخة الاحتياطية القديمة: ${file.path}');
        }
      }
    } catch (e) {
      AppLogger.error('خطأ أثناء تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) => _resetInactivityTimer(),
      onPointerMove: (_) => _resetInactivityTimer(),
      child: widget.child,
    );
  }
}

/// مدير الخروج من التطبيق المحسن
///
/// يتعامل مع طلبات الخروج من التطبيق ويعرض مربع حوار للتأكيد
/// يمكن تخصيص مظهر مربع الحوار وإضافة إجراءات قبل الخروج من التطبيق
class AppExitHandler extends StatelessWidget {
  /// الويدجت الابن
  final Widget child;

  /// عنوان مربع حوار التأكيد
  final String title;

  /// محتوى مربع حوار التأكيد
  final String content;

  /// نص زر الإلغاء
  final String cancelText;

  /// نص زر الخروج
  final String exitText;

  /// لون زر الإلغاء
  final Color? cancelButtonColor;

  /// لون زر الخروج
  final Color? exitButtonColor;

  /// أيقونة زر الإلغاء
  final IconData? cancelIcon;

  /// أيقونة زر الخروج
  final IconData? exitIcon;

  /// هل يتم إنشاء نسخة احتياطية قبل الخروج
  final bool createBackupBeforeExit;

  /// دالة تنفذ قبل الخروج من التطبيق
  final Future<void> Function()? onBeforeExit;

  /// دالة تنفذ بعد الضغط على زر الإلغاء
  final VoidCallback? onCancelPressed;

  const AppExitHandler({
    Key? key,
    required this.child,
    this.title = 'تأكيد الخروج',
    this.content = 'هل تريد الخروج من التطبيق؟',
    this.cancelText = 'إلغاء',
    this.exitText = 'خروج',
    this.cancelButtonColor,
    this.exitButtonColor,
    this.cancelIcon,
    this.exitIcon,
    this.createBackupBeforeExit = true,
    this.onBeforeExit,
    this.onCancelPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام PopScope الجديد بدلاً من WillPopScope المهجور
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final shouldPop = await _showExitConfirmationDialog(context);
        if (shouldPop == true && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: child,
    );
  }

  /// عرض مربع حوار تأكيد الخروج
  Future<bool?> _showExitConfirmationDialog(BuildContext context) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () {
              if (onCancelPressed != null) {
                onCancelPressed!();
              }
              Navigator.of(context).pop(false);
            },
            style: cancelButtonColor != null
                ? ButtonStyle(
                    foregroundColor: WidgetStatePropertyAll(cancelButtonColor))
                : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (cancelIcon != null) ...[
                  Icon(cancelIcon),
                  const SizedBox(width: 8)
                ],
                Text(cancelText),
              ],
            ),
          ),
          TextButton(
            onPressed: () async {
              try {
                // إنشاء نسخة احتياطية قبل الخروج إذا كان مطلوباً
                if (createBackupBeforeExit) {
                  final isLoggedIn = await SessionManager.isLoggedIn();
                  if (isLoggedIn) {
                    AppLogger.info('إنشاء نسخة احتياطية قبل الخروج من التطبيق');
                    await BackupService.createBackup();
                    AppLogger.info(
                        'تم إنشاء نسخة احتياطية قبل الخروج من التطبيق');
                  }
                }

                // تنظيف الذاكرة والملفات المؤقتة قبل الخروج
                try {
                  final tempDir = await getTemporaryDirectory();
                  // تنظيف الملفات المؤقتة قبل الخروج
                  if (await tempDir.exists()) {
                    try {
                      final tempFiles = await tempDir
                          .list()
                          .where((entity) => entity is File)
                          .cast<File>()
                          .toList();

                      for (final file in tempFiles) {
                        try {
                          await file.delete();
                        } catch (e) {
                          // تجاهل الأخطاء للملفات الفردية
                        }
                      }

                      AppLogger.info(
                          'تم تنظيف الملفات المؤقتة قبل الخروج من التطبيق');
                    } catch (e) {
                      AppLogger.warning('تعذر تنظيف بعض الملفات المؤقتة: $e');
                    }
                  }
                } catch (e) {
                  AppLogger.error(
                      'خطأ أثناء تنظيف الملفات المؤقتة قبل الخروج: $e');
                }

                // تنفيذ الإجراءات المخصصة قبل الخروج
                if (onBeforeExit != null) {
                  await onBeforeExit!();
                }

                if (context.mounted) {
                  Navigator.of(context).pop(true);
                }
              } catch (e) {
                AppLogger.error('خطأ أثناء تنفيذ إجراءات الخروج: $e');
                if (context.mounted) {
                  Navigator.of(context).pop(false);
                }
              }
            },
            style: exitButtonColor != null
                ? ButtonStyle(
                    foregroundColor: WidgetStatePropertyAll(exitButtonColor))
                : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (exitIcon != null) ...[
                  Icon(exitIcon),
                  const SizedBox(width: 8)
                ],
                Text(exitText),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
