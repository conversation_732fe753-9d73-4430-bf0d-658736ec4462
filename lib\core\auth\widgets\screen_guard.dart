import 'package:flutter/material.dart';
import '../../../core/widgets/ak_widgets.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/theme/app_dimensions.dart';
import '../../../core/theme/dynamic_colors.dart';
import '../services/auth_service.dart';
import '../../../core/utils/app_logger.dart';

/// ودجت حماية الشاشات
/// يتحقق من صلاحية المستخدم قبل عرض الشاشة
class ScreenGuard extends StatefulWidget {
  /// الصلاحية المطلوبة للوصول للشاشة
  final String permission;

  /// قائمة الصلاحيات المطلوبة (يجب توفر جميعها)
  final List<String>? requiredPermissions;

  /// قائمة الصلاحيات البديلة (يكفي توفر واحدة منها)
  final List<String>? anyPermissions;

  /// الشاشة المراد حمايتها
  final Widget child;

  /// الشاشة البديلة في حالة عدم وجود صلاحية
  final Widget? fallbackScreen;

  /// هل يتم إعادة التوجيه للشاشة الرئيسية في حالة عدم وجود صلاحية
  final bool redirectToHome;

  /// رسالة مخصصة لعدم وجود صلاحية
  final String? noPermissionMessage;

  /// عنوان الشاشة (يظهر في شريط التطبيق)
  final String? title;

  /// دالة تنفذ عند عدم وجود صلاحية
  final VoidCallback? onNoPermission;

  const ScreenGuard({
    Key? key,
    required this.permission,
    required this.child,
    this.requiredPermissions,
    this.anyPermissions,
    this.fallbackScreen,
    this.redirectToHome = false,
    this.noPermissionMessage,
    this.title,
    this.onNoPermission,
  }) : super(key: key);

  /// إنشاء حماية للوحدة
  factory ScreenGuard.module({
    Key? key,
    required String module,
    required Widget child,
    Widget? fallbackScreen,
    bool redirectToHome = false,
    String? noPermissionMessage,
    String? title,
    VoidCallback? onNoPermission,
  }) {
    return ScreenGuard(
      key: key,
      permission: 'view_$module',
      child: child,
      fallbackScreen: fallbackScreen,
      redirectToHome: redirectToHome,
      noPermissionMessage: noPermissionMessage,
      title: title,
      onNoPermission: onNoPermission,
    );
  }

  @override
  State<ScreenGuard> createState() => _ScreenGuardState();
}

class _ScreenGuardState extends State<ScreenGuard> {
  final AuthService _authService = AuthService();
  bool _hasPermission = false;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// التحقق من الصلاحيات
  Future<void> _checkPermissions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      bool hasPermission = false;

      // التحقق من الصلاحية الأساسية
      if (widget.permission.isNotEmpty) {
        hasPermission = await _authService.hasPermission(widget.permission);
      }

      // التحقق من الصلاحيات المطلوبة (جميعها)
      if (!hasPermission && widget.requiredPermissions != null) {
        hasPermission = await _authService.hasAllPermissions(widget.requiredPermissions!);
      }

      // التحقق من الصلاحيات البديلة (واحدة منها)
      if (!hasPermission && widget.anyPermissions != null) {
        hasPermission = await _authService.hasAnyPermission(widget.anyPermissions!);
      }

      setState(() {
        _hasPermission = hasPermission;
        _isLoading = false;
      });

      // تنفيذ دالة عدم وجود صلاحية
      if (!hasPermission && widget.onNoPermission != null) {
        widget.onNoPermission!();
      }

      // إعادة التوجيه للشاشة الرئيسية إذا طُلب ذلك
      if (!hasPermission && widget.redirectToHome && mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Navigator.of(context).pushReplacementNamed('/dashboard');
        });
      }

      AppLogger.info('🔐 نتيجة فحص صلاحية الشاشة ${widget.permission}: ${hasPermission ? 'مسموح' : 'مرفوض'}');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasPermission = false;
        _errorMessage = 'خطأ في التحقق من الصلاحية: $e';
      });
      AppLogger.error('❌ خطأ في فحص صلاحية الشاشة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض شاشة التحميل أثناء فحص الصلاحيات
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    // إذا كان هناك خطأ
    if (_errorMessage != null) {
      return _buildErrorScreen();
    }

    // إذا كانت الصلاحية متوفرة، عرض الشاشة
    if (_hasPermission) {
      return widget.child;
    }

    // عرض الشاشة البديلة أو شاشة عدم وجود صلاحية
    return widget.fallbackScreen ?? _buildNoPermissionScreen();
  }

  /// بناء شاشة التحميل
  Widget _buildLoadingScreen() {
    return Scaffold(
      appBar: widget.title != null
          ? AkAppBar(
              title: widget.title!,
            )
          : null,
      body: const Center(
        child: AkLoadingIndicator(
          message: 'جاري التحقق من الصلاحيات...',
        ),
      ),
    );
  }

  /// بناء شاشة الخطأ
  Widget _buildErrorScreen() {
    return Scaffold(
      appBar: widget.title != null
          ? AkAppBar(
              title: widget.title!,
            )
          : null,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              const SizedBox(height: AppDimensions.spacingLarge),
              Text(
                'خطأ في التحقق من الصلاحيات',
                style: AppTypography.headlineSmall.copyWith(
                  color: AppColors.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingMedium),
              Text(
                _errorMessage ?? 'خطأ غير معروف',
                style: AppTypography.bodyMedium.copyWith(
                  color: DynamicColors.textSecondary(context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingLarge),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AkButton(
                    text: 'إعادة المحاولة',
                    onPressed: _checkPermissions,
                    icon: Icons.refresh,
                  ),
                  const SizedBox(width: AppDimensions.spacingMedium),
                  AkButton(
                    text: 'العودة',
                    onPressed: () => Navigator.of(context).pop(),
                    variant: AkButtonVariant.outlined,
                    icon: Icons.arrow_back,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شاشة عدم وجود صلاحية
  Widget _buildNoPermissionScreen() {
    return Scaffold(
      appBar: widget.title != null
          ? AkAppBar(
              title: widget.title!,
            )
          : null,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: DynamicColors.textSecondary(context),
              ),
              const SizedBox(height: AppDimensions.spacingLarge),
              Text(
                'غير مصرح بالوصول',
                style: AppTypography.headlineSmall.copyWith(
                  color: DynamicColors.textPrimary(context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingMedium),
              Text(
                widget.noPermissionMessage ?? 
                'ليس لديك صلاحية للوصول إلى هذه الشاشة.\nيرجى التواصل مع المسؤول لطلب الصلاحية المطلوبة.',
                style: AppTypography.bodyMedium.copyWith(
                  color: DynamicColors.textSecondary(context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingSmall),
              Text(
                'الصلاحية المطلوبة: ${widget.permission}',
                style: AppTypography.bodySmall.copyWith(
                  color: DynamicColors.textSecondary(context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingLarge),
              AkButton(
                text: 'العودة للشاشة الرئيسية',
                onPressed: () => Navigator.of(context).pushReplacementNamed('/dashboard'),
                icon: Icons.home,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
