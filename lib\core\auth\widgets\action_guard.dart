import 'package:flutter/material.dart';
import '../../../core/widgets/ak_widgets.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/theme/app_dimensions.dart';
import '../../../core/theme/dynamic_colors.dart';
import '../services/auth_service.dart';
import '../../../core/utils/app_logger.dart';

/// ودجت حماية العمليات والأزرار
/// يتحقق من صلاحية المستخدم قبل تنفيذ العملية
class ActionGuard extends StatefulWidget {
  /// الصلاحية المطلوبة لتنفيذ العملية
  final String permission;

  /// قائمة الصلاحيات المطلوبة (يجب توفر جميعها)
  final List<String>? requiredPermissions;

  /// قائمة الصلاحيات البديلة (يكفي توفر واحدة منها)
  final List<String>? anyPermissions;

  /// الودجت المراد حمايته (عادة زر أو عنصر تفاعلي)
  final Widget child;

  /// الودجت البديل في حالة عدم وجود صلاحية
  final Widget? fallback;

  /// هل يتم إخفاء الودجت بالكامل إذا لم تكن الصلاحية متوفرة
  final bool hideIfNoPermission;

  /// دالة تنفذ عند الضغط (مع التحقق من الصلاحية)
  final VoidCallback? onPressed;

  /// رسالة تظهر عند عدم وجود صلاحية
  final String? noPermissionMessage;

  /// هل يتم عرض رسالة تنبيه عند عدم وجود صلاحية
  final bool showNoPermissionDialog;

  const ActionGuard({
    Key? key,
    required this.permission,
    required this.child,
    this.requiredPermissions,
    this.anyPermissions,
    this.fallback,
    this.hideIfNoPermission = false,
    this.onPressed,
    this.noPermissionMessage,
    this.showNoPermissionDialog = true,
  }) : super(key: key);

  /// إنشاء حماية لعملية CRUD
  factory ActionGuard.crud({
    Key? key,
    required String module,
    required String operation,
    required Widget child,
    Widget? fallback,
    bool hideIfNoPermission = false,
    VoidCallback? onPressed,
    String? noPermissionMessage,
    bool showNoPermissionDialog = true,
  }) {
    String permission;
    switch (operation.toLowerCase()) {
      case 'create':
      case 'add':
        permission = 'add_${module.replaceAll('s', '')}';
        break;
      case 'read':
      case 'view':
        permission = 'view_$module';
        break;
      case 'update':
      case 'edit':
        permission = 'edit_${module.replaceAll('s', '')}';
        break;
      case 'delete':
        permission = 'delete_${module.replaceAll('s', '')}';
        break;
      default:
        permission = 'view_$module';
    }

    return ActionGuard(
      key: key,
      permission: permission,
      child: child,
      fallback: fallback,
      hideIfNoPermission: hideIfNoPermission,
      onPressed: onPressed,
      noPermissionMessage: noPermissionMessage,
      showNoPermissionDialog: showNoPermissionDialog,
    );
  }

  @override
  State<ActionGuard> createState() => _ActionGuardState();
}

class _ActionGuardState extends State<ActionGuard> {
  final AuthService _authService = AuthService();
  bool _hasPermission = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// التحقق من الصلاحيات
  Future<void> _checkPermissions() async {
    try {
      setState(() {
        _isLoading = true;
      });

      bool hasPermission = false;

      // التحقق من الصلاحية الأساسية
      if (widget.permission.isNotEmpty) {
        hasPermission = await _authService.hasPermission(widget.permission);
      }

      // التحقق من الصلاحيات المطلوبة (جميعها)
      if (!hasPermission && widget.requiredPermissions != null) {
        hasPermission = await _authService.hasAllPermissions(widget.requiredPermissions!);
      }

      // التحقق من الصلاحيات البديلة (واحدة منها)
      if (!hasPermission && widget.anyPermissions != null) {
        hasPermission = await _authService.hasAnyPermission(widget.anyPermissions!);
      }

      setState(() {
        _hasPermission = hasPermission;
        _isLoading = false;
      });

      AppLogger.info('🔐 نتيجة فحص صلاحية العملية ${widget.permission}: ${hasPermission ? 'مسموح' : 'مرفوض'}');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasPermission = false;
      });
      AppLogger.error('❌ خطأ في فحص صلاحية العملية: $e');
    }
  }

  /// تنفيذ العملية مع التحقق من الصلاحية
  void _handleAction() {
    if (_hasPermission) {
      // تنفيذ العملية إذا كانت الصلاحية متوفرة
      if (widget.onPressed != null) {
        widget.onPressed!();
      }
    } else {
      // عرض رسالة عدم وجود صلاحية
      if (widget.showNoPermissionDialog) {
        _showNoPermissionDialog();
      }
    }
  }

  /// عرض مربع حوار عدم وجود صلاحية
  void _showNoPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.lock_outline,
              color: AppColors.warning,
            ),
            const SizedBox(width: AppDimensions.spacingSmall),
            const Text('غير مصرح'),
          ],
        ),
        content: Text(
          widget.noPermissionMessage ?? 
          'ليس لديك صلاحية لتنفيذ هذه العملية.\nيرجى التواصل مع المسؤول لطلب الصلاحية المطلوبة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // عرض مؤشر التحميل أثناء فحص الصلاحيات
    if (_isLoading) {
      return const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    // إذا كانت الصلاحية متوفرة، عرض الودجت مع إمكانية التفاعل
    if (_hasPermission) {
      return _wrapWithAction(widget.child);
    }

    // إذا لم تكن الصلاحية متوفرة
    if (widget.hideIfNoPermission) {
      return const SizedBox.shrink();
    }

    // عرض الودجت البديل أو الودجت المعطل
    return widget.fallback ?? _buildDisabledWidget();
  }

  /// تغليف الودجت بالعملية
  Widget _wrapWithAction(Widget child) {
    // إذا كان الودجت زر، نحتاج للتعامل معه بشكل خاص
    if (child is AkButton) {
      return AkButton(
        text: child.text,
        onPressed: _handleAction,
        icon: child.icon,
        variant: child.variant,
        size: child.size,
        isLoading: child.isLoading,
        isEnabled: child.isEnabled,
      );
    }

    // إذا كان الودجت عادي، نغلفه بـ GestureDetector أو InkWell
    if (widget.onPressed != null) {
      return InkWell(
        onTap: _handleAction,
        child: child,
      );
    }

    return child;
  }

  /// بناء ودجت معطل
  Widget _buildDisabledWidget() {
    Widget disabledChild = widget.child;

    // إذا كان الودجت زر، نجعله معطل
    if (widget.child is AkButton) {
      final button = widget.child as AkButton;
      disabledChild = AkButton(
        text: button.text,
        onPressed: widget.showNoPermissionDialog ? _handleAction : null,
        icon: button.icon,
        variant: button.variant,
        size: button.size,
        isEnabled: false,
      );
    }

    // تغليف الودجت بشفافية للإشارة إلى أنه معطل
    return Opacity(
      opacity: 0.5,
      child: IgnorePointer(
        ignoring: !widget.showNoPermissionDialog,
        child: InkWell(
          onTap: widget.showNoPermissionDialog ? _handleAction : null,
          child: disabledChild,
        ),
      ),
    );
  }
}

/// دالة مساعدة للتحقق من الصلاحية قبل تنفيذ عملية
Future<bool> checkPermissionBeforeAction({
  required BuildContext context,
  required String permission,
  String? noPermissionMessage,
  bool showDialog = true,
}) async {
  final authService = AuthService();
  final hasPermission = await authService.hasPermission(permission);

  if (!hasPermission && showDialog && context.mounted) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.lock_outline,
              color: AppColors.warning,
            ),
            const SizedBox(width: AppDimensions.spacingSmall),
            const Text('غير مصرح'),
          ],
        ),
        content: Text(
          noPermissionMessage ?? 
          'ليس لديك صلاحية لتنفيذ هذه العملية.\nيرجى التواصل مع المسؤول لطلب الصلاحية المطلوبة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  return hasPermission;
}
